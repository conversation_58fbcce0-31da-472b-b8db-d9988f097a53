import {
  CommonActions,
  createNavigationContainerRef,
} from '@react-navigation/native';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
// import Home from '../Screen/Page/Home';

export enum RootScreen {
  Intro = 'Intro',
  splashView = 'Splash',
  Instructors = 'Instructors',
  DetailNews = 'DetailNews',
  navigateEComParent = 'navigateEComParent',
  navigateEComView = 'navigateEComView',
  navigateCommunityParent = 'navigateCommunityParent',
  navigateCommunityView = 'navigateCommunityView',
  navigateSakupiParent = 'navigateSakupiParent',
  navigateSakupiView = 'navigateSakupiView',
  DrawerNavigation = 'DrawerNavigation',
  RegisterShop = 'RegisterShop',
  FAQView = 'FAQView',
  PolicyView = 'PolicyView',
  DetailPost = 'DetailPost',
  login = 'Login',
  SettingProfile = 'SettingProfile',
  BiometricSetting = 'BiometricSetting',
  ForgotPass = 'ForgotPass',
  order = 'ORDER',
  Shop = 'Shop',
  VnpayPaymentScreen = 'VnpayPaymentScreen',
  PurchaseHistory = 'PurchaseHistory',
  // Demo screens
  // notification
  Notification = 'Notification',
  NotifCommunity = 'NotifCommunity',
  ProductDetail = 'ProductDetail',
  OrderDetailPage = 'OrderDetailPage',
  CartPage = 'CartPage',
  CheckoutPage = 'CheckoutPage',
  HotProductsDemo = 'HotProductsDemo',
  AllHotProductsPage = 'AllHotProductsPage',
  OrderDetail = 'OrderDetail',
  Review = 'Review',
  ManageProduct = 'ManageProduct',
  CreateNewProduct = 'CreateNewProduct',
  ListProductCreate = 'ListProductCreate',
  ListItemChild = 'ListItemChild',
  LableProduct = 'LableProduct',
  OriginProduct = 'OriginProduct',
  ChartReport = 'ChartReport',
  ProductListByCategory = 'ProductListByCategory',
  MyWallet = 'MyWallet',
  SearchIndex = 'SearchIndex',
  CreateReviewOrderDetail="CreateReviewOrderDetail",
  CreateReviewOrder="CreateReviewOrder",
  ConfigAffiliate = 'ConfigAffiliate',
  TreeAffiliateDetail = 'TreeAffiliateDetail',
  MyWalletProfile = 'MyWalletProfile'
  
}

export const navigationRef = createNavigationContainerRef();
export function navigate(routeName: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.navigate(routeName, params));
  }
}

export function navigateBack() {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(CommonActions.goBack());
  }
}
export function navigateBackWithParams(screenName: string, params?: any) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.navigate({
        name: screenName,
        params: params,
      }),
    );
  }
}
export function navigateReset(routeName: string, params?: object) {
  if (navigationRef.isReady()) {
    navigationRef.dispatch(
      CommonActions.reset({
        index: 0,
        routes: [{name: routeName, params}],
      }),
    );
  }
}
