import React, { useState } from 'react';
import {
    ScrollView,
    View,
    Text,
    StyleSheet,
    TouchableOpacity,
    Dimensions,
    FlatList
} from "react-native";
import HeaderShop from "../../components/shop/HeaderShop";
import NavigateShop from "../../components/shop/NavigateShop";
import { ColorThemes } from "../../assets/skin/colors";
import { TypoSkin } from "../../assets/skin/typography";
import { Winicon, ComponentStatus, showSnackbar } from 'wini-mobile-components';
import QRCode from 'react-native-qrcode-svg';
import LinearGradient from 'react-native-linear-gradient';
import Clipboard from '@react-native-clipboard/clipboard';
import { useSelectorCustomerState } from "../../redux/hook/customerHook";
import { navigate, RootScreen } from "../../router/router";

const { width } = Dimensions.get('window');

const MyWalletProfile = () => {
    const customer = useSelectorCustomerState().data;

    // Mock data - thay thế bằng data thực từ API
    const walletBalance = 123141000;
    const incomeAmount = 12032000;
    const expenseAmount = 4021000;
    const walletId = "e12eased3413da-sd3123-1231412-kuaj";

    const transactionHistory = [
        {
            id: 1,
            type: 'income',
            amount: 20000000,
            description: 'Hoa hồng đơn hàng #1234545 từ "Nguyễn Thanh Tùng"',
            time: '17:40 - 06 Dec 2024',
            icon: 'fill/user interface/plus'
        },
        {
            id: 2,
            type: 'expense',
            amount: -20000000,
            description: 'Hoa hồng đơn hàng #1234545 từ "Nguyễn Thanh Tùng"',
            time: '17:40 - 06 Dec 2024',
            icon: 'fill/user interface/minus'
        }
    ];

    const copyToClipboard = () => {
        Clipboard.setString(walletId);
        showSnackbar({
            status: ComponentStatus.SUCCSESS,
            message: 'Đã sao chép ID wallet',
        });
    };

    const formatMoney = (amount: number) => {
        return amount.toLocaleString('vi-VN');
    };

    const navigateToTransactionHistory = () => {
        navigate(RootScreen.TransactionHistory);
    };

    const renderTransactionItem = ({ item }: any) => (
        <View style={styles.transactionItem}>
            <View style={[
                styles.transactionIcon,
                { backgroundColor: item.type === 'income' ? ColorThemes.light.secondary2_background : ColorThemes.light.error_background }
            ]}>
                <Winicon
                    src={item.icon}
                    size={16}
                    color={item.type === 'income' ? ColorThemes.light.secondary2_main_color : ColorThemes.light.error_main_color}
                />
            </View>
            <View style={styles.transactionContent}>
                <View style={styles.transactionHeader}>
                    <Text style={[
                        styles.transactionAmount,
                        { color: item.type === 'income' ? ColorThemes.light.secondary2_main_color : ColorThemes.light.error_main_color }
                    ]}>
                        {item.type === 'income' ? '+' : ''}{formatMoney(item.amount)} GCT
                    </Text>
                </View>
                <Text style={styles.transactionDescription} numberOfLines={2}>
                    {item.description}
                </Text>
                <Text style={styles.transactionTime}>
                    {item.time}
                </Text>
            </View>
        </View>
    );

    return (
        <ScrollView style={styles.container}>
            <HeaderShop />
            <NavigateShop title={'Ví của tôi'} />

            {/* QR Code Section */}
            <View style={styles.qrSection}>
                <View style={styles.qrContainer}>
                    <LinearGradient
                        colors={['#4A90E2', '#7B68EE', '#9370DB']}
                        style={styles.qrGradient}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 1 }}
                    >
                        <View style={styles.qrCodeWrapper}>
                            <QRCode
                                value={walletId}
                                size={80}
                                backgroundColor="white"
                                color="black"
                            />
                        </View>
                    </LinearGradient>
                </View>

                <View style={styles.walletInfo}>
                    <Text style={styles.walletIdLabel}>ID wallet</Text>
                    <Text style={styles.walletIdText} numberOfLines={2}>
                        {walletId}
                    </Text>

                    <View style={styles.actionButtons}>
                        <TouchableOpacity style={styles.actionButton} onPress={copyToClipboard}>
                            <Text style={styles.actionButtonText}>Copy</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.actionButton}>
                            <Text style={styles.actionButtonText}>Chuyển CAN nội bộ</Text>
                        </TouchableOpacity>
                        <TouchableOpacity style={styles.actionButton}>
                            <Text style={styles.actionButtonText}>Rút CAN point</Text>
                        </TouchableOpacity>
                    </View>
                </View>
            </View>

            {/* Balance Section */}
            <View style={styles.balanceSection}>
                <Text style={styles.balanceLabel}>Số dư ví</Text>
                <Text style={styles.balanceAmount}>{formatMoney(walletBalance)}</Text>
            </View>

            {/* Income/Expense Cards */}
            <View style={styles.statsSection}>
                <View style={[styles.statsCard, { backgroundColor: ColorThemes.light.secondary2_background }]}>
                    <Winicon
                        src="fill/user interface/trending-up"
                        size={24}
                        color={ColorThemes.light.secondary2_main_color}
                    />
                    <Text style={styles.statsLabel}>Income</Text>
                    <Text style={[styles.statsAmount, { color: ColorThemes.light.secondary2_main_color }]}>
                        {formatMoney(incomeAmount)}
                    </Text>
                </View>

                <View style={[styles.statsCard, { backgroundColor: ColorThemes.light.primary_background }]}>
                    <Winicon
                        src="fill/user interface/trending-down"
                        size={24}
                        color={ColorThemes.light.primary_main_color}
                    />
                    <Text style={styles.statsLabel}>Expense</Text>
                    <Text style={[styles.statsAmount, { color: ColorThemes.light.primary_main_color }]}>
                        {formatMoney(expenseAmount)}
                    </Text>
                </View>
            </View>

            {/* Transaction History */}
            <View style={styles.historySection}>
                <View style={styles.historyHeader}>
                    <Text style={styles.historyTitle}>Lịch sử giao dịch</Text>
                    <TouchableOpacity onPress={navigateToTransactionHistory}>
                        <Text style={styles.viewAllText}>Xem tất cả</Text>
                    </TouchableOpacity>
                </View>

                <FlatList
                    data={transactionHistory}
                    renderItem={renderTransactionItem}
                    keyExtractor={(item) => item.id.toString()}
                    scrollEnabled={false}
                    showsVerticalScrollIndicator={false}
                />
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#f8f9fa',
    },
    qrSection: {
        flexDirection: 'row',
        paddingHorizontal: 20,
        paddingVertical: 20,
        backgroundColor: 'white',
        marginBottom: 10,
        alignItems: 'center',
    },
    qrContainer: {
        marginRight: 15,
    },
    qrGradient: {
        width: 120,
        height: 120,
        borderRadius: 60,
        justifyContent: 'center',
        alignItems: 'center',
    },
    qrCodeWrapper: {
        backgroundColor: 'white',
        padding: 15,
        borderRadius: 15,
    },
    walletInfo: {
        flex: 1,
    },
    walletIdLabel: {
        fontSize: 12,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginBottom: 4,
    },
    walletIdText: {
        fontSize: 14,
        color: ColorThemes.light.neutral_text_title_color,
        fontWeight: '500',
        marginBottom: 12,
    },
    actionButtons: {
        gap: 8,
    },
    actionButton: {
        backgroundColor: ColorThemes.light.primary_background,
        paddingHorizontal: 12,
        paddingVertical: 8,
        borderRadius: 20,
        alignSelf: 'flex-start',
        marginBottom: 4,
    },
    actionButtonText: {
        fontSize: 12,
        color: ColorThemes.light.primary_main_color,
        fontWeight: '500',
    },
    balanceSection: {
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingVertical: 20,
        marginBottom: 10,
        alignItems: 'center',
    },
    balanceLabel: {
        fontSize: 14,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginBottom: 8,
    },
    balanceAmount: {
        fontSize: 32,
        fontWeight: 'bold',
        color: ColorThemes.light.primary_main_color,
    },
    statsSection: {
        flexDirection: 'row',
        paddingHorizontal: 20,
        gap: 12,
        marginBottom: 10,
    },
    statsCard: {
        flex: 1,
        padding: 16,
        borderRadius: 12,
        alignItems: 'center',
    },
    statsLabel: {
        fontSize: 14,
        color: ColorThemes.light.neutral_text_subtitle_color,
        marginTop: 8,
        marginBottom: 4,
    },
    statsAmount: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    historySection: {
        backgroundColor: 'white',
        paddingHorizontal: 20,
        paddingTop: 20,
        paddingBottom: 10,
    },
    historyHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 16,
    },
    historyTitle: {
        fontSize: 16,
        fontWeight: '600',
        color: ColorThemes.light.neutral_text_title_color,
    },
    viewAllText: {
        fontSize: 14,
        color: ColorThemes.light.primary_main_color,
        fontWeight: '500',
    },
    transactionItem: {
        flexDirection: 'row',
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: ColorThemes.light.neutral_border_color,
    },
    transactionIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        justifyContent: 'center',
        alignItems: 'center',
        marginRight: 12,
    },
    transactionContent: {
        flex: 1,
    },
    transactionHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: 4,
    },
    transactionAmount: {
        fontSize: 16,
        fontWeight: '600',
    },
    transactionDescription: {
        fontSize: 14,
        color: ColorThemes.light.neutral_text_title_color,
        marginBottom: 4,
        lineHeight: 20,
    },
    transactionTime: {
        fontSize: 12,
        color: ColorThemes.light.neutral_text_subtitle_color,
    },
});

export default MyWalletProfile;
